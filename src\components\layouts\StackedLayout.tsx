import React from 'react';
import { LayoutProps } from '@/lib/layouts';
import MediaItemRenderer from './MediaItemRenderer';

const StackedLayout: React.FC<LayoutProps> = ({ content, topic, elementIndex }) => {
  // Assumes single text item
  const item = content[0];
  return (
    <div className="w-full h-full">
      {item && <MediaItemRenderer item={item} topic={topic} elementIndex={elementIndex} className="w-full h-full" />}
    </div>
  );
};

export default StackedLayout;
