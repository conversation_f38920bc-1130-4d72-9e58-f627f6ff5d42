import React from 'react';
import { LayoutVariant, LayoutProps } from './layouts';

// Import layout components
import DualLayout from '@/components/layouts/DualLayout';
import TripleLayout from '@/components/layouts/TripleLayout';
import GridLayout from '@/components/layouts/GridLayout';
import StackedLayout from '@/components/layouts/StackedLayout';
import CustomCulinaryLayout from '@/components/layouts/CustomCulinaryLayout';
import HeroLayout from '@/components/layouts/HeroLayout'; // Импортируем новый компонент
import FreeLayout from '@/components/layouts/FreeLayout';

export const layoutRegistry: Record<LayoutVariant, React.FC<LayoutProps>> = {
  dual: DualLayout,
  triple: TripleLayout,
  'grid-2x2': GridLayout,
  stacked: StackedLayout,
  custom_culinary: CustomCulinaryLayout,
  hero: HeroLayout, // Регистрируем его
  free: FreeLayout,
};
