'use client';

import { create } from 'zustand';

// --- Типы данных, основанные на вашем JSON --- //

export interface ContentItem {
  type: 'text' | 'image' | 'video';
  value?: string; // для текста
  url?: string;   // для изображений/видео
  caption?: {
    title: string;
    body: string;
  };
}

export interface Element {
  id: string;
  topic?: string;
  layout?: string; // Для кастомных раскладок, например 'custom_culinary'
  position: { x: number; y: number; z: number };
  content: ContentItem[];
  // Добавим размеры для удобства, по умолчанию 500x300
  width: number;
  height: number;
}

export interface NarrativeStep {
  step_id: string;
  target_id: string | null;
  zoom: number;
  speech: string;
}

export interface PresentationData {
  title: string;
  world_bounds: { x: number; y: number; z: number };
  elements: Element[];
  narrative_path: NarrativeStep[];
}

// --- Структура состояния в Zustand --- //

export interface PresentationState {
  title: string;
  elements: Record<string, Element>; // Элементы в виде объекта для быстрого доступа по ID
  narrative_path: NarrativeStep[];
  currentStepIndex: number;
  viewport: { x: number; y: number; zoom: number };
  currentSpeech: string;
  world_bounds: { x: number; y: number; z: number };

  // Действия
  setInitialState: (data: PresentationData) => void;
  goToStep: (stepIndex: number) => void;
  goToNextStep: () => void;
  goToPrevStep: () => void;
}

export const useStore = create<PresentationState>((set, get) => ({
  // Начальное состояние
  title: '',
  elements: {},
  narrative_path: [],
  currentStepIndex: -1,
  viewport: { x: 0, y: 0, zoom: 1 },
  currentSpeech: '',
  world_bounds: { x: 4000, y: 3000, z: 0 },

  // --- Действия --- //

  setInitialState: (data) => {
    // Преобразуем массив элементов в объект для быстрого доступа
    const elementsRecord = data.elements.reduce((acc, el) => {
      // Задаем размеры по умолчанию, если они не указаны
      acc[el.id] = { ...el, width: el.width || 500, height: el.height || 300 };
      return acc;
    }, {} as Record<string, Element>);

    set({
      title: data.title,
      elements: elementsRecord,
      narrative_path: data.narrative_path,
      world_bounds: data.world_bounds,
      currentStepIndex: -1, // Начинаем "перед" первым шагом
    });

    // Сразу переходим к первому шагу
    get().goToStep(0);
  },

  goToStep: (stepIndex) => {
    const { narrative_path, elements, world_bounds } = get();

    if (stepIndex < 0 || stepIndex >= narrative_path.length) {
      return; // Выход, если индекс за пределами диапазона
    }

    const step = narrative_path[stepIndex];
    const targetId = step.target_id;
    const targetElement = targetId ? elements[targetId] : null;

    const newViewport = targetElement
      ? { // Фокусируемся на элементе
          x: targetElement.position.x,
          y: targetElement.position.y,
          zoom: step.zoom,
        }
      : { // Общий вид (target_id: null)
          x: world_bounds.x / 2, // Центр мира
          y: world_bounds.y / 2,
          zoom: step.zoom, // Общий зум
        };

    set({
      currentStepIndex: stepIndex,
      currentSpeech: step.speech,
      viewport: newViewport,
    });
  },

  goToNextStep: () => {
    const { currentStepIndex, narrative_path } = get();
    if (currentStepIndex < narrative_path.length - 1) {
      get().goToStep(currentStepIndex + 1);
    }
  },

  goToPrevStep: () => {
    const { currentStepIndex } = get();
    if (currentStepIndex > 0) {
      get().goToStep(currentStepIndex - 1);
    }
  },
}));
