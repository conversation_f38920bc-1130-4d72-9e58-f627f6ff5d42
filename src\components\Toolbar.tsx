// src/components/Toolbar.tsx
'use client';
import { useStore } from '@/store/presentationStore';

const Toolbar = () => {
  const goToNextStep = useStore(state => state.goToNextStep);
  const goToPrevStep = useStore(state => state.goToPrevStep);
  const currentStepIndex = useStore(state => state.currentStepIndex);
  const narrative_path = useStore(state => state.narrative_path);
  const currentSpeech = useStore(state => state.currentSpeech);

  const totalSteps = narrative_path.length;
  const isPrevDisabled = currentStepIndex <= 0;
  const isNextDisabled = currentStepIndex >= totalSteps - 1;

  return (
    <div className="toolbar bg-white shadow-md p-2 flex flex-col md:flex-row items-center justify-between z-10 gap-4">
      <div className="flex items-center gap-4">
        <button
          onClick={goToPrevStep}
          disabled={isPrevDisabled}
          className="px-4 py-2 bg-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Назад
        </button>
        <span className="font-mono text-sm">
          Шаг: {currentStepIndex > -1 ? currentStepIndex + 1 : '-'} / {totalSteps}
        </span>
        <button
          onClick={goToNextStep}
          disabled={isNextDisabled}
          className="px-4 py-2 bg-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Вперед
        </button>
      </div>
      <div className="flex-grow text-center px-4 min-w-0">
        <p className="text-sm text-gray-700 italic truncate">{currentSpeech || '...'}</p>
      </div>
    </div>
  );
};

export default Toolbar;
