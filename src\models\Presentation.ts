// src/models/Presentation.ts
// Этот файл будет содержать Mongoose схему и модель для презентаций.
// Пока что это заглушка.

// import mongoose, { Schema, Document, Model } from 'mongoose';

// // Определяем интерфейс для элемента презентации
// interface IElement extends Document {
//   id: string;
//   type: 'frame' | 'text'; // и другие типы
//   x: number;
//   y: number;
//   width?: number;
//   height?: number;
//   content?: string;
//   // другие специфичные для типа свойства
// }

// // Определяем интерфейс для данных презентации, которые хранятся в БД
// interface IPresentationData {
//   elements: Record<string, Omit<IElement, '_id' | '__v'>>;
//   path: string[];
//   // возможно, viewport или начальные настройки
// }

// // Определяем интерфейс для документа презентации
// export interface IPresentation extends Document {
//   userId: mongoose.Types.ObjectId; // Ссылка на пользователя, если нужна авторизация
//   title: string;
//   data: IPresentationData;
//   createdAt: Date;
//   updatedAt: Date;
// }

// const ElementSchema = new Schema<IElement>({
//   id: { type: String, required: true },
//   type: { type: String, required: true, enum: ['frame', 'text'] },
//   x: { type: Number, required: true },
//   y: { type: Number, required: true },
//   width: { type: Number },
//   height: { type: Number },
//   content: { type: String },
// }, { _id: false }); // _id не нужен для вложенных элементов, если id уникален

// const PresentationDataSchema = new Schema<IPresentationData>({
//   elements: {
//     type: Map,
//     of: ElementSchema,
//     required: true,
//   },
//   path: [{ type: String, required: true }],
// }, { _id: false });

// const PresentationSchema = new Schema<IPresentation>(
//   {
//     // userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
//     title: { type: String, required: true, default: 'Untitled Presentation' },
//     data: { type: PresentationDataSchema, required: true },
//   },
//   { timestamps: true } // Добавляет createdAt и updatedAt
// );

// // Предотвращаем перекомпиляцию модели при hot-reload в Next.js
// const Presentation: Model<IPresentation> = mongoose.models.Presentation || mongoose.model<IPresentation>('Presentation', PresentationSchema);

// export default Presentation;

// Заглушка, чтобы проект собирался
export default {};
