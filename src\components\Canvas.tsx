// src/components/Canvas.tsx
'use client';

import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { useStore, Element } from '@/store/presentationStore';
import { layoutRegistry } from '@/lib/layoutRegistry';
import { inferLayout, LayoutVariant } from '@/lib/layouts';

interface CanvasProps {
  elements: Element[];
}

const Canvas: React.FC<CanvasProps> = ({ elements }) => {
  const viewport = useStore(state => state.viewport);
  const world_bounds = useStore(state => state.world_bounds); // Получаем размеры мира
  const videoRefs = useRef<Record<string, HTMLVideoElement | null>>({});

  useEffect(() => {
    Object.keys(videoRefs.current).forEach(key => {
      const videoEl = videoRefs.current[key];
      if (videoEl) {
        const [elementId, contentIndexStr] = key.split('-');
        const contentIndex = parseInt(contentIndexStr, 10);
        const element = elements.find(el => el.id === elementId);

        if (element && element.content[contentIndex]?.type === 'video') {
          const isFocused = viewport.x === element.position.x && viewport.y === element.position.y;
          if (isFocused) {
            videoEl.play().catch(error => console.warn("Video play failed or interrupted:", error));
          } else {
            videoEl.pause();
            // videoEl.currentTime = 0; // Раскомментируйте, если нужно сбрасывать видео к началу
          }
        }
      }
    });
  }, [viewport, elements]);

  return (
    <div className="w-full h-full bg-gray-200 relative overflow-hidden">
      <motion.div
        className="absolute" // Убираем CSS-переходы, так как Framer Motion будет управлять анимацией
        animate={{
          scale: viewport.zoom,
          x: -viewport.x * viewport.zoom,
          y: -viewport.y * viewport.zoom,
        }}
        transition={{
          type: 'spring',
          stiffness: 90, // Уменьшаем жесткость для более мягкой пружины
          damping: 30,  // Увеличиваем затухание для более плавной остановки
        }}
        style={{
          left: '50%',
          top: '50%',
          transformOrigin: '0 0', // The world's (0,0) is now at screen center; scale/translate relative to it.
          width: `${world_bounds.x}px`, // Устанавливаем ширину мира
          height: `${world_bounds.y}px`, // Устанавливаем высоту мира
        }}
      >
        {elements.map((el, elIndex) => (
          <motion.div
            key={el.id}
            className="absolute bg-white shadow-lg rounded-lg p-4 flex flex-col justify-center items-center gap-2 overflow-hidden"
            style={{
              // Позиционируем элемент относительно его центра
              left: `${el.position.x}px`,
              top: `${el.position.y}px`,
              width: `${el.width}px`,
              height: `${el.height}px`,
              transform: 'translate(-50%, -50%)', // Компенсируем сдвиг для центрирования
            }}
          >
            <h3 className="text-xl font-bold text-gray-800 shrink-0">{el.topic}</h3>
            <div className="flex-grow w-full relative p-1">
              {(() => {
                const LayoutComponent = layoutRegistry[el.layout as LayoutVariant || inferLayout(el.content)];
                if (!LayoutComponent) {
                  return <div>Error: Layout '{el.layout || 'inferred'}' not found.</div>;
                }
                return <LayoutComponent content={el.content} topic={el.topic} elementIndex={elIndex} />;
              })()}
            </div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export default Canvas;
