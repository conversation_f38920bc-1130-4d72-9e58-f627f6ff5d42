// src/app/api/presentations/[id]/route.ts
import { NextResponse } from 'next/server';
// import { connectToDB } from '@/lib/mongodb'; // Ваша функция для подключения к БД
// import Presentation from '@/models/Presentation'; // Ваша Mongoose-модель

// Временное хранилище в памяти для демонстрации
const presentationsStore: Record<string, any> = {};

// Обработчик для PUT-запроса (обновление/создание)
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id } = params;
    const body = await request.json(); // Получаем данные из тела запроса

    // await connectToDB(); // Раскомментировать при подключении БД
    // await Presentation.findByIdAndUpdate(id, { data: body.data }, { upsert: true, new: true }); // upsert: true создаст, если не найдено

    // Имитация сохранения в БД
    presentationsStore[id] = body.data;
    console.log(`Presentation ${id} updated/created:`, presentationsStore[id]);

    return NextResponse.json({ message: 'Presentation updated successfully', data: presentationsStore[id] }, { status: 200 });
  } catch (error) {
    console.error('Error updating presentation:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ message: 'Error updating presentation', error: errorMessage }, { status: 500 });
  }
}

// Обработчик для GET-запроса (получение)
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id } = params;
    // await connectToDB();
    // const presentation = await Presentation.findById(id);

    // Имитация получения из БД
    const presentationData = presentationsStore[id];

    if (!presentationData) {
      return NextResponse.json({ message: 'Presentation not found' }, { status: 404 });
    }
    console.log(`Presentation ${id} fetched:`, presentationData);
    return NextResponse.json(presentationData, { status: 200 });
  } catch (error) {
    console.error('Error fetching presentation:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ message: 'Error fetching presentation', error: errorMessage }, { status: 500 });
  }
}
