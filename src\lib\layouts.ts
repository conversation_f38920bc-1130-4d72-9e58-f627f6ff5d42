import { ContentItem } from '@/store/presentationStore';

export type LayoutVariant =
  | 'dual'
  | 'triple'
  | 'grid-2x2'
  | 'stacked'
  | 'custom_culinary'
  | 'hero' // Наш новый вариант
  | 'free';

export interface LayoutProps {
  content: ContentItem[];
  topic?: string;
  elementIndex?: number;
}

export function inferLayout(content: ContentItem[]): LayoutVariant {
  if (content.length === 5 && content.some(c => c.type === 'text')) {
    return 'custom_culinary';
  }
  if (content.length === 4) {
    return 'grid-2x2';
  }
  if (content.length === 3) {
    return 'triple';
  }
  if (content.length === 2) {
    return 'dual';
  }
  if (content.length === 1) {
    if (content[0].type === 'image') return 'hero'; // Новое правило для 'hero'
    if (content[0].type === 'text') return 'stacked';
  }
  return 'free'; // Для одиночного видео или других непредусмотренных случаев
}
