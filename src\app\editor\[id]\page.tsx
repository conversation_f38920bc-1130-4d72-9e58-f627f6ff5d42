// src/app/editor/[id]/page.tsx
import Editor from '@/components/Editor';
import { PresentationData, PresentationState } from '@/store/presentationStore';
import { Metadata, ResolvingMetadata } from 'next';

// Эта функция выполняется на сервере!
async function getPresentationData(id: string): Promise<PresentationData> {
  // Используем ваши моковые данные
  const mockData: PresentationData = {
    "title": "1547 El Camino Del Teatro, La Jolla",
    "world_bounds": { "x": 4000, "y": 3000, "z": 0 },
    "elements": [
      {
        "id": "intro_background",
        "topic": "1547 El Camino Del Teatro, La Jolla",
        "position": { "x": 2000, "y": 1500, "z": 0 },
        "width": 1200, "height": 675,
        "content": [{ "type": "video", "url": "/demo-assets/intro.mp4" }]
      },
      {
        "id": "exterior_details_block",
        "topic": "Экстерьер: Дв<PERSON><PERSON><PERSON><PERSON>й взгляд",
        "position": { "x": 3500, "y": 1500, "z": 0 },
        "width": 1300, "height": 675,
        "content": [
          {
            "type": "video",
            "url": "/demo-assets/step2.mp4",
            "caption": {
              "title": "Live Lavish in La Jolla",
              "body": "A marvel of sculptural and exceptional design slowly reveals its magnificence from a secluded drive on one of the most exclusive streets in La Jolla. The over 1 1/2-acre estate balances panoramic ocean views and vivid natural topography with a sophisticated materiality and nuanced colors."
            }
          },
          { "type": "image", "url": "/demo-assets/step2.png" }
        ]
      },
      {
        "id": "culinary_experience_collage",
        "topic": "Кулинарный опыт",
        "layout": "custom_culinary", 
        "position": { "x": 5500, "y": 1500, "z": 0 },
        "width": 1300, "height": 750, 
        "content": [
          {
            "type": "text",
            "value": "An Elevated Culinary Experience\n\nEvery detail exudes luxury, from the custom Italian marble countertops to the handcrafted cabinetry adorned with polished brass hardware. Indulge your senses with top-of-the-line appliances, including a professional-grade Wolf range, Sub-Zero refrigerator, and a built-in Miele espresso machine."
          },
          {
            "type": "image",
            "url": "/demo-assets/step3_1.jpg" 
          },
          {
            "type": "image",
            "url": "/demo-assets/step2.png"
          },
          {
            "type": "image",
            "url": "/demo-assets/step3_1.jpg" 
          },
          {
            "type": "image",
            "url": "/demo-assets/step2.png",
            "caption": {
              "title": "A Spectacular Dining Ambiance",
              "body": "The home comes equipped with a luxurious wine cellar – a testament to both form and function – boasting bespoke shelving to showcase your finest vintages. Whether you're hosting a dinner party or creating a gourmet masterpiece, the home offers an unparalleled experience in sophistication and functionality."
            }
          }
        ]
      },
      {
        "id": "kitchen_block",
        "topic": "Кухня",
        "position": { "x": 500, "y": 800, "z": 0 },
        "width": 600, "height": 400,
        "content": [
          { "type": "image", "url": "/demo-assets/livingroom.jpg" },
          { "type": "text", "value": "Площадь 12 кв.м.\nТехника Bosch" }
        ]
      },
      {
        "id": "livingroom_block",
        "topic": "Гостиная",
        "position": { "x": 2000, "y": 2500, "z": 0 },
        "width": 800, "height": 600,
        "content": [
          { "type": "image", "url": "/demo-assets/livingroom.jpg" },
          { "type": "text", "value": "Панорамные окна\nВысота потолков 3.2м" }
        ]
      },
      {
        "id": "bedroom_block",
        "topic": "Спальня",
        "position": { "x": 3500, "y": 800, "z": 0 },
        "width": 700, "height": 500,
        "content": [
          { "type": "image", "url": "/demo-assets/bedroom.jpg" },
          { "type": "video", "url": "/demo-assets/cat.mp4" },
          { "type": "text", "value": "Окна выходят в тихий двор" }
        ]
      }
    ],
    "narrative_path": [
      {
        "step_id": "overview_intro",
        "target_id": "intro_background",
        "zoom": 1.0,
        "speech": "Добро пожаловать в 1547 El Camino Del Teatro, La Jolla. Давайте осмотримся."
      },
      {
        "step_id": "step2_exterior",
        "target_id": "exterior_details_block",
        "zoom": 1.0,
        "speech": "Рассмотрим экстерьер с двух ракурсов."
      },
      {
        "step_id": "step3_culinary",
        "target_id": "culinary_experience_collage",
        "zoom": 0.9, // Может потребоваться корректировка
        "speech": "Давайте погрузимся в атмосферу кулинарных изысков этого дома."
      },
      {
        "step_id": "step4",
        "target_id": "bedroom_block",
        "zoom": 1.5,
        "speech": "И, наконец, уютная спальня."
      }

    ]
  };
  return mockData;
}

export async function generateMetadata(
  { params }: { params: { id: string } }
): Promise<Metadata> {
  // Явно ожидаем params, чтобы удовлетворить Next.js для динамических маршрутов
  const id = (await Promise.resolve(params)).id;
  const presentationData = await getPresentationData(id);
  return {
    title: presentationData.title,
  };
}

// Серверный компонент для страницы редактора
export default async function EditorPage({ params }: { params: { id: string } }) {
  const id = (await Promise.resolve(params)).id;
  console.log(`%c%s%c Fetching data for presentation ID: ${id}`, 'background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px', ' Server ', '');

  // Имитация загрузки данных презентации сервере и передаем их в клиентский компонент Editor
  const presentationData = await getPresentationData(id);
  return <Editor initialData={presentationData} />;
}
