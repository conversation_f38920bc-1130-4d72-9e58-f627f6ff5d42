import React from 'react';
import Image from 'next/image';
import { LayoutProps } from '@/lib/layouts';

const HeroLayout: React.FC<LayoutProps> = ({ content, topic, elementIndex }) => {
  const heroItem = content.find(c => c.type === 'image');

  if (!heroItem || !heroItem.url) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-200 text-gray-500">
        Missing Hero Image
      </div>
    );
  }

  return (
    <div className="w-full h-full relative rounded overflow-hidden">
      <Image
        src={heroItem.url}
        alt={heroItem.caption?.title || topic || 'Hero image'}
        layout="fill"
        objectFit="cover"
        priority={elementIndex === 0}
      />
      <div className="absolute inset-0 bg-black/40 flex items-center justify-center p-4">
        {topic && <h2 className="text-4xl md:text-6xl font-bold text-white text-center drop-shadow-lg">{topic}</h2>}
      </div>
    </div>
  );
};

export default HeroLayout;
