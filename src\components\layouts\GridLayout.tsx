import React from 'react';
import { LayoutProps } from '@/lib/layouts';
import MediaItemRenderer from './MediaItemRenderer';

const GridLayout: React.FC<LayoutProps> = ({ content, topic, elementIndex }) => {
  return (
    <div className="w-full h-full grid grid-cols-2 grid-rows-2 gap-1">
      {content.map((item, idx) => (
        <MediaItemRenderer key={item.url || idx} item={item} topic={topic} index={idx} elementIndex={elementIndex} className="w-full h-full" />
      ))}
    </div>
  );
};

export default GridLayout;
