import React from 'react';
import { LayoutProps } from '@/lib/layouts';
import MediaItemRenderer from './MediaItemRenderer';

// Triple layout is visually identical to dual, just with 3 items instead of 2.
// The flex properties handle the distribution.
const TripleLayout: React.FC<LayoutProps> = ({ content, topic, elementIndex }) => {
  return (
    <div className="w-full h-full flex flex-row items-stretch justify-center gap-1">
      {content.map((item, idx) => (
        <MediaItemRenderer key={item.url || idx} item={item} topic={topic} index={idx} elementIndex={elementIndex} className="flex-1 min-h-0 w-0" />
      ))}
    </div>
  );
};

export default TripleLayout;
