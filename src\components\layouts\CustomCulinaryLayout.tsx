import React from 'react';
import Image from 'next/image';
import { LayoutProps } from '@/lib/layouts';
import { ContentItem } from '@/store/presentationStore';

const CustomCulinaryLayout: React.FC<LayoutProps> = ({ content }) => {
  const textItem = content.find(c => c.type === 'text');
  const imageItems = content.filter(c => c.type === 'image');
  
  // This layout is rigid and expects a specific order/number of items
  if (imageItems.length < 4 || !textItem) {
    return <div>Invalid content for CustomCulinaryLayout</div>;
  }

  const [foodImageItem, kitchenImageItem, wineCellarImageItem, wineGlassesItem] = imageItems;

  const parseTextValue = (inputValue?: string | { title: string; body: string }): { title: string; body: string } => {
    if (!inputValue) return { title: '', body: '' };

    if (typeof inputValue === 'string') {
      const valueStr = inputValue.trim();
      if (valueStr === '') return { title: '', body: '' };
      let title = valueStr, body = '';
      const periodMatch = valueStr.match(/^([^.]+)\.([\s\S]*)$/);
      if (periodMatch) {
        title = periodMatch[1].trim();
        body = periodMatch[2].trim();
      }
      return { title, body };
    } else if (typeof inputValue === 'object' && inputValue !== null) {
      return { title: inputValue.title?.trim() || '', body: inputValue.body?.trim() || '' };
    }
    return { title: '', body: '' };
  };

  const culinaryText = parseTextValue(textItem?.value);
  const ambianceText = parseTextValue(wineGlassesItem?.caption);

  return (
    <div className="w-full h-full grid grid-cols-2 grid-rows-[2fr_3fr] gap-1">
      {/* Top-Left: Text + Image */}
      <div className="col-start-1 row-start-1 flex overflow-hidden rounded bg-black text-white">
        <div className="w-1/2 p-4 md:p-6 overflow-y-auto custom-scrollbar flex flex-col justify-center">
          {culinaryText.title && <h3 className="text-xl md:text-2xl font-semibold mb-2 md:mb-3 leading-tight">{culinaryText.title}</h3>}
          {culinaryText.body && <p className="text-xs md:text-sm leading-relaxed">{culinaryText.body}</p>}
        </div>
        {foodImageItem?.url && <div className="w-1/2 relative"><Image src={foodImageItem.url} alt={foodImageItem.caption?.title || culinaryText.title || 'Food'} layout="fill" objectFit="cover" priority /></div>}
      </div>

      {/* Top-Right: Image */}
      {wineCellarImageItem?.url && <div className="col-start-2 row-start-1 relative rounded overflow-hidden"><Image src={wineCellarImageItem.url} alt={wineCellarImageItem.caption?.title || 'Wine cellar'} layout="fill" objectFit="cover" priority /></div>}
      
      {/* Bottom-Left: Image */}
      {kitchenImageItem?.url && <div className="col-start-1 row-start-2 relative rounded overflow-hidden"><Image src={kitchenImageItem.url} alt={kitchenImageItem.caption?.title || 'Kitchen'} layout="fill" objectFit="cover" /></div>}

      {/* Bottom-Right: Image + Text */}
      <div className="col-start-2 row-start-2 flex overflow-hidden rounded bg-black text-white">
        {wineGlassesItem?.url && <div className="w-1/2 relative"><Image src={wineGlassesItem.url} alt={ambianceText.title || 'Dining ambiance'} layout="fill" objectFit="cover" /></div>}
        <div className="w-1/2 p-4 md:p-6 overflow-y-auto custom-scrollbar flex flex-col justify-center">
          {ambianceText.title && <h3 className="text-xl md:text-2xl font-semibold mb-2 md:mb-3 leading-tight">{ambianceText.title}</h3>}
          {ambianceText.body && <p className="text-xs md:text-sm leading-relaxed">{ambianceText.body}</p>}
        </div>
      </div>
    </div>
  );
};

export default CustomCulinaryLayout;
