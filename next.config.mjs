/** @type {import('next').NextConfig} */
const nextConfig = {
  async headers() {
    return [
      {
        // Применяем эти заголовки ко всем маршрутам в приложении
        source: '/:path*',
        headers: [
          {
            key: 'Content-Security-Policy',
            // Заменяем стандартную политику. Разрешаем медиа с нашего домена ('self').
            value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; media-src 'self'; font-src 'self'; frame-src 'self';",
          },
        ],
      },
    ];
  },
};

export default nextConfig;
