// src/components/Editor.tsx
'use client'; // <-- Очень важно! Делает компонент клиентским.

import React, { useEffect, useMemo } from 'react';
import { useStore, PresentationData } from '@/store/presentationStore'; 
import Canvas from './Canvas'; // Компонент холста
import Toolbar from './Toolbar'; // Панель инструментов
// import Sidebar from './Sidebar'; // Возможно, понадобится сайдбар

interface EditorProps {
  initialData: PresentationData;
}

const Editor = ({ initialData }: EditorProps) => {
  const setInitialState = useStore((state) => state.setInitialState);
  const elements = useStore((state) => state.elements);

  useEffect(() => {
    if (initialData) {
      setInitialState(initialData);
    }
  }, [initialData, setInitialState]);

  const elementsArray = useMemo(() => Object.values(elements), [elements]);

  return (
    <div className="editor-layout w-screen h-screen flex flex-col bg-gray-100" style={{ border: '5px solid black' }}  >
      <Toolbar />
      <div className="flex-grow relative overflow-hidden">
        <Canvas elements={elementsArray} />
      </div>
      {/* <Sidebar /> */}
    </div>
  );
};

export default Editor;
