import React from 'react';
import Image from 'next/image';
import { ContentItem } from '@/store/presentationStore';

interface MediaItemRendererProps {
  item: ContentItem;
  className?: string;
  topic?: string;
  index?: number;
  elementIndex?: number;
}

const MediaItemRenderer: React.FC<MediaItemRendererProps> = ({ item, className, topic, index = 0, elementIndex = 0 }) => {
  const itemWrapperClassName = `relative rounded overflow-hidden shadow-lg ${className}`;

  switch (item.type) {
    case 'image':
      return (
        <div className={itemWrapperClassName}>
          {item.url && <Image
            src={item.url}
            alt={(item.caption?.title && item.caption?.body) ? `${item.caption.title}: ${item.caption.body}` : item.caption?.title || item.caption?.body || `${topic || 'Image'} ${index + 1}`}
            layout="fill"
            objectFit="cover"
            priority={elementIndex === 0 && index === 0}
          />}
          {(item.caption?.title || item.caption?.body) && (
            <div className="absolute bottom-0 left-0 right-0 p-2 bg-black/60 text-white text-xs text-center">
              {item.caption.title ? `${item.caption.title}${item.caption.body ? `: ${item.caption.body}` : ''}` : (item.caption.body || null)}
            </div>
          )}
        </div>
      );
    case 'video':
      return (
        <div className={`${itemWrapperClassName} bg-black`}>
          {item.url && <video src={item.url} className="w-full h-full object-cover" controls autoPlay muted loop playsInline />}
          {(item.caption?.title || item.caption?.body) && (
            <div className="absolute bottom-0 left-0 right-0 p-2 bg-black/60 text-white text-xs text-center">
              {item.caption.title ? `${item.caption.title}${item.caption.body ? `: ${item.caption.body}` : ''}` : (item.caption.body || null)}
            </div>
          )}
        </div>
      );
    case 'text':
      return (
        <div className={`${itemWrapperClassName} p-4 bg-black/70 text-white overflow-y-auto custom-scrollbar`}>
          <div dangerouslySetInnerHTML={{ __html: item.value || '' }} />
        </div>
      );
    default:
      return null;
  }
};

export default MediaItemRenderer;
